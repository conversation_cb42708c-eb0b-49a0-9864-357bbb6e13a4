<template>
  <common-dialog
    title="签到奖励配置"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
    :show-footer="true"
    @closed="handleClosed"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="dialog-main">
      <el-form ref="ruleForm" :model="form" :rules="rules" label-position="top">
        <el-form-item label="签到规则配置" prop="ruleContent">
          <wang-editor ref="ruleContent" v-model="form.ruleContent" :content.sync="form.ruleContent" :height="300" />
        </el-form-item>
        <el-form-item label="签到提醒文案" prop="remindText">
          <el-input v-model="form.remindText" type="textarea" :maxlength="20" show-word-limit placeholder="请输入签到提醒文案" />
        </el-form-item>
        <el-form-item label="签到奖励配置（铁粉用户享奖励翻倍权益）">
          <el-table :data="form.rewardList" border size="small" class="table-container">
            <el-table-column label="连续签到" width="120" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.days }}天</span>
              </template>
            </el-table-column>
            <el-table-column label="奖励智米数" align="center">
              <template slot-scope="scope">
                <el-form-item
                  :prop="'rewardList.' + scope.$index + '.points'"
                  :rules="rules.points"
                  :show-message="false"
                  class="table-form-item"
                >
                  <el-input-number v-model="scope.row.points" :min="0" :max="999" size="mini" controls-position="right" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="rewardListError" class="el-form-item__error" style="position: relative;">
            {{ rewardListError }}
          </div>
        </el-form-item>
        <el-form-item label="变更记录">
          <el-table :data="paginatedRecords" border size="small" class="table-container">
            <el-table-column prop="editor" label="编辑人" align="center" />
            <el-table-column prop="content" label="编辑内容" align="center" />
            <el-table-column prop="editTime" label="编辑时间" width="180" align="center" />
          </el-table>
          <div class="yz-table-pagination">
            <pagination
              v-show="total > 0"
              size="mini"
              :total="total"
              :page.sync="listQuery.page"
              :limit.sync="listQuery.limit"
              @pagination="getList"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import WangEditor from '@/components/WangEditor';
import Pagination from '@/components/Pagination';
export default {
  name: 'SignRuleDialog',

  components: {
    WangEditor,
    Pagination
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      form: {
        ruleContent: '',
        remindText: '',
        rewardList: []
      },
      rules: {
        ruleContent: [
          { required: true, message: '请输入签到规则内容', trigger: 'blur' }
        ],
        remindText: [
          { required: true, message: '请输入签到提醒文案', trigger: 'blur' },
          { max: 20, message: '提醒文案不能超过20个字符', trigger: 'blur' }
        ],
        points: [
          { required: true, message: '请填写奖励智米数', trigger: 'blur' }
        ]
      },
      rewardListError: null,
      total: 0,
      listQuery: {
        page: 1,
        limit: 10
      },
      changeRecords: []
    };
  },

  computed: {
    paginatedRecords() {
      const start = (this.listQuery.page - 1) * this.listQuery.limit;
      const end = start + this.listQuery.limit;
      return this.changeRecords.slice(start, end);
    }
  },

  watch: {
    visible(val) {
      if (val) {
        this.initData();
      }
    },
    'form.rewardList': {
      deep: true,
      handler(newVal) {
        if (this.rewardListError) {
          const isStillInvalid = newVal.some(item => item.points === undefined || item.points === null);
          if (!isStillInvalid) {
            this.rewardListError = null;
          }
        }
      }
    }
  },

  methods: {
    // 初始化数据
    async initData() {
      try {
        // 并发调用两个接口
        const [configResult, rewardResult] = await Promise.all([
          this.$http.get('/operate/sign/config/detail/1', { json: true }),
          this.$http.get('/operate/sign/reward/all', { json: true })
        ]);

        if (configResult.code === '00' && rewardResult.code === '00') {
          // 处理签到配置数据
          const configData = configResult.body;
          // 处理奖励数据
          const rewardList = rewardResult.body.map(item => ({
            days: item.signDays,
            points: item.rewardZhimi
          }));

          this.form = {
            ruleContent: configData.signRuleDesc || '',
            remindText: configData.signTip || '',
            rewardList: rewardList.length > 0 ? rewardList : Array.from({ length: 7 }, (_, i) => ({
              days: i + 1,
              points: undefined
            }))
          };
          this.$refs.ruleContent.setContent(this.form.ruleContent);
          // 获取变更记录
          await this.fetchChangeRecords();
        } else {
          throw new Error('获取数据失败');
        }
      } catch (error) {
        console.error(error);
        this.$message.error('获取数据失败');
        this.$refs.ruleContent.setContent(this.form.ruleContent);
        // 即使出错也尝试获取变更记录
        await this.fetchChangeRecords();
      }
    },

    // 获取变更记录
    async fetchChangeRecords() {
      try {
        const params = {
          operateBusinessType: 3,
          pageNum: this.listQuery.page.toString(),
          pageSize: this.listQuery.limit.toString()
        };

        const result = await this.$http.post(
          '/operate/common/list',
          params,
          { json: true }
        );

        if (result.code === '00') {
          this.changeRecords = result.body.data.map(item => ({
            editor: item.operateUser,
            content: item.operateContent,
            editTime: item.operateTime
          }));
          this.total = result.body.recordsTotal;
        } else {
          this.changeRecords = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('获取变更记录失败:', error);
        this.changeRecords = [];
        this.total = 0;
      }
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.$emit('update:visible', false);
    },

    handleConfirm() {
      this.rewardListError = null;
      this.$refs.ruleForm.validate((valid, invalidFields) => {
        if (valid) {
          this.handleSaveSignRule(this.form);
        } else {
          if (invalidFields && Object.keys(invalidFields).some(key => key.startsWith('rewardList.'))) {
            this.rewardListError = '请填写所有天数的奖励智米数';
          }
        }
      });
    },

    handleClosed() {
      this.$refs.ruleForm.resetFields();
      this.form = {
        ruleContent: '',
        remindText: '',
        rewardList: []
      };
      this.changeRecords = [];
      this.listQuery.page = 1;
      this.total = 0;
    },

    // 保存签到规则
    async handleSaveSignRule(data) {
      try {
        // 准备配置数据
        const configParams = {
          signRuleDesc: data.ruleContent,
          signTip: data.remindText
        };

        // 准备奖励数据
        const rewardParams = {
          rewardList: data.rewardList.map(item => ({
            signDays: item.days,
            rewardZhimi: item.points
          })),
          operateContent: '批量更新签到奖励配置'
        };

        // 并发调用两个接口
        const [configResult, rewardResult] = await Promise.all([
          this.$http.post('/operate/sign/config/edit', configParams, { json: true }),
          this.$http.post('/operate/sign/reward/batchUpdate', rewardParams, { json: true })
        ]);

        if (configResult.code === '00' && rewardResult.code === '00') {
          this.$message.success('保存成功');
          this.$emit('update:visible', false);
        } else {
          this.$message.error(configResult.body.message || rewardResult.body.message || '保存失败');
        }
      } catch (error) {
        console.error(error);
      }
    },

    getList() {
      // 分页变化时重新获取变更记录
      this.fetchChangeRecords();
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number {
  width: 100%;
}

.yz-table-pagination {
  text-align: right;
}

.table-form-item {
  margin-bottom: 0;
}
</style>
