{"swagger": "2.0", "info": {"title": "APP项目组--后台", "version": "last"}, "basePath": "/", "tags": [{"name": "签到配置管理接口", "description": "签到规则配置、签到奖励配置、签到赚取配置、用户签到流水的完整管理功能"}], "schemes": ["http"], "paths": {"/operate/sign/earn/edit": {"post": {"tags": ["签到配置管理接口"], "summary": "修改签到赚取", "description": "修改签到赚取配置", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "主标题", "mock": {"mock": "邀好友,赢豪礼"}}, "subTitle": {"type": "string", "description": "副标题", "mock": {"mock": "最高可得 30000智米"}}, "icon": {"type": "string", "description": "图标路由", "mock": {"mock": "/static/images/invite.png"}}, "route": {"type": "string", "description": "路由", "mock": {"mock": "/invite"}}, "sort": {"type": "string", "description": "权重", "mock": {"mock": "9996"}}, "ifAllow": {"type": "integer", "description": "是否启用", "mock": {"mock": "1"}}, "id": {"type": "integer", "description": "主键id"}}, "required": ["title", "subTitle", "icon", "route", "sort", "ifAllow", "id"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}}}}}, "/operate/sign/record/user/{userId}": {"get": {"tags": ["签到配置管理接口"], "summary": "废弃---根据用户ID获取签到流水", "description": "根据用户ID获取该用户的所有签到流水记录", "consumes": ["application/json"], "parameters": [{"name": "userId", "in": "path", "description": "用户ID", "required": true, "type": "string"}, {"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {}, "required": []}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "userId": {"type": "string", "description": "用户ID"}, "signDate": {"type": "string", "description": "签到日期"}, "signDays": {"type": "integer", "description": "连续签到天数"}, "rewardZhimi": {"type": "integer", "description": "奖励智米数"}, "createTime": {"type": "string", "description": "创建时间"}, "yzCode": {"type": "string", "description": "远智编号"}, "realName": {"type": "string", "description": "真实姓名"}, "nickname": {"type": "string", "description": "昵称"}, "mobileNumber": {"type": "string", "description": "手机号码"}, "cycleSignDays": {"type": "integer", "description": "周期内连续签到天数"}}, "required": ["id", "userId", "signDate", "signDays", "<PERSON><PERSON><PERSON><PERSON>", "createTime", "yzCode", "realName", "nickname", "mobileNumber", "cycleSignDays"]}}}, "required": ["code", "message", "data"]}}}}}, "/operate/sign/earn/enabled": {"get": {"tags": ["签到配置管理接口"], "summary": "废弃---获取启用的签到赚取配置", "description": "获取所有启用的签到赚取配置", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {}, "required": []}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "title": {"type": "string", "description": "主标题"}, "subTitle": {"type": "string", "description": "副标题"}, "icon": {"type": "string", "description": "图标路由"}, "route": {"type": "string", "description": "路由"}, "sort": {"type": "string", "description": "权重"}, "ifAllow": {"type": "integer", "description": "是否启用"}}, "required": ["id", "title", "subTitle", "icon", "route", "sort", "ifAllow"]}}}, "required": ["code", "message", "data"]}}}}}, "/operate/sign/record/detail/{id}": {"get": {"tags": ["签到配置管理接口"], "summary": "废弃---获取用户签到流水详情", "description": "根据ID获取用户签到流水详情", "consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "description": "签到流水ID", "required": true, "type": "string"}, {"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {}, "required": []}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "userId": {"type": "string", "description": "用户ID"}, "signDate": {"type": "string", "description": "签到日期"}, "signDays": {"type": "integer", "description": "连续签到天数"}, "rewardZhimi": {"type": "integer", "description": "奖励智米数"}, "createTime": {"type": "string", "description": "创建时间"}, "yzCode": {"type": "string", "description": "远智编号"}, "realName": {"type": "string", "description": "真实姓名"}, "nickname": {"type": "string", "description": "昵称"}, "mobileNumber": {"type": "string", "description": "手机号码"}, "cycleSignDays": {"type": "integer", "description": "周期内连续签到天数"}}, "required": ["id", "userId", "signDate", "signDays", "<PERSON><PERSON><PERSON><PERSON>", "createTime", "yzCode", "realName", "nickname", "mobileNumber", "cycleSignDays"]}}, "required": ["code", "message", "data"]}}}}}, "/operate/sign/reward/list": {"post": {"tags": ["签到配置管理接口"], "summary": "废弃---获取签到奖励列表", "description": "获取签到奖励列表，支持分页和条件查询", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"pageNum": {"type": "integer", "description": "页码", "mock": {"mock": "1"}}, "pageSize": {"type": "integer", "description": "每页数量", "mock": {"mock": "10"}}, "signDays": {"type": "integer", "description": "连续签到天数"}, "createUser": {"type": "string", "description": "创建人"}, "startTime": {"type": "string", "description": "开始时间"}, "endTime": {"type": "string", "description": "结束时间"}}, "required": ["pageNum", "pageSize"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "signDays": {"type": "integer", "description": "连续签到天数"}, "rewardZhimi": {"type": "integer", "description": "奖励智米数"}, "createTime": {"type": "string", "description": "创建时间"}, "createUserId": {"type": "string", "description": "创建人ID"}, "createUser": {"type": "string", "description": "创建人"}}, "required": ["id", "signDays", "<PERSON><PERSON><PERSON><PERSON>", "createTime", "createUserId", "createUser"]}}}, "required": ["total", "list"]}}, "required": ["code", "message", "data"]}}}}}, "/operate/sign/reward/batchUpdate": {"post": {"tags": ["签到配置管理接口"], "summary": "批量修改签到奖励", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"rewardList": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "signDays": {"type": "integer", "description": "连续签到天数"}, "rewardZhimi": {"type": "integer", "description": "奖励智米数"}}, "required": ["id", "signDays", "<PERSON><PERSON><PERSON><PERSON>"]}}, "operateContent": {"type": "string", "description": "操作内容"}}, "required": ["rewardList", "operateContent"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}}}}}, "/operate/sign/reward/batchAdd": {"post": {"tags": ["签到配置管理接口"], "summary": "批量新增签到奖励", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"rewardList": {"type": "array", "items": {"type": "object", "properties": {"signDays": {"type": "integer", "description": "连续签到天数"}, "rewardZhimi": {"type": "integer", "description": "奖励智米数"}}, "required": ["signDays", "<PERSON><PERSON><PERSON><PERSON>"]}}}, "required": ["rewardList"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}}}}}, "/operate/sign/earn/add": {"post": {"tags": ["签到配置管理接口"], "summary": "新增签到赚取", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "主标题", "mock": {"mock": "邀好友,赢豪礼"}}, "subTitle": {"type": "string", "description": "副标题", "mock": {"mock": "最高可得 30000智米"}}, "icon": {"type": "string", "description": "图标路由", "mock": {"mock": "/static/images/invite.png"}}, "route": {"type": "string", "description": "路由", "mock": {"mock": "/invite"}}, "sort": {"type": "string", "description": "权重", "mock": {"mock": "9996"}}, "ifAllow": {"type": "integer", "description": "是否启用", "mock": {"mock": "1"}}}, "required": ["title", "subTitle", "icon", "route", "sort", "ifAllow"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}}}}}, "/operate/sign/config/add": {"post": {"tags": ["签到配置管理接口"], "summary": "新增签到配置", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"signRuleDesc": {"type": "string", "description": "签到规则描述", "mock": {"mock": "每日签到可获得智米奖励，连续签到奖励翻倍"}}, "signTip": {"type": "string", "description": "签到提醒文案", "mock": {"mock": "记得每日签到哦"}}}, "required": ["signRuleDesc", "signTip"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}}}}}, "/operate/sign/config/edit": {"post": {"tags": ["签到配置管理接口"], "summary": "更新签到配置", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "signRuleDesc": {"type": "string", "description": "签到规则描述", "mock": {"mock": "每日签到可获得智米奖励，连续签到奖励翻倍"}}, "signTip": {"type": "string", "description": "签到提醒文案", "mock": {"mock": "记得每日签到哦"}}}, "required": ["id", "signRuleDesc", "signTip"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}}, "required": ["code", "message"]}}}}}, "/operate/sign/reward/all": {"get": {"tags": ["签到配置管理接口"], "summary": "获取所有签到奖励配置", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {}, "required": []}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "signDays": {"type": "integer", "description": "连续签到天数"}, "rewardZhimi": {"type": "integer", "description": "奖励智米数"}}, "required": ["id", "signDays", "<PERSON><PERSON><PERSON><PERSON>"]}}}, "required": ["code", "message", "data"]}}}}}, "/operate/sign/record/list": {"post": {"tags": ["签到配置管理接口"], "summary": "获取用户签到流水列表", "description": "获取用户签到流水列表，支持按真实姓名、远智编号、手机号码、签到时间范围等条件查询", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"pageNum": {"type": "integer", "description": "页码", "mock": {"mock": "1"}}, "pageSize": {"type": "integer", "description": "每页数量", "mock": {"mock": "10"}}, "realName": {"type": "string", "description": "真实姓名"}, "yzCode": {"type": "string", "description": "远智编号"}, "mobile": {"type": "string", "description": "手机号码"}, "startTime": {"type": "string", "description": "开始时间"}, "endTime": {"type": "string", "description": "结束时间"}}, "required": ["pageNum", "pageSize"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"recordsTotal": {"type": "integer", "description": "总记录数"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "userId": {"type": "string", "description": "用户ID"}, "signDate": {"type": "string", "description": "签到日期"}, "signDays": {"type": "integer", "description": "连续签到天数"}, "rewardZhimi": {"type": "integer", "description": "奖励智米数"}, "createTime": {"type": "string", "description": "创建时间"}, "yzCode": {"type": "string", "description": "远智编号"}, "realName": {"type": "string", "description": "真实姓名"}, "nickname": {"type": "string", "description": "昵称"}, "mobile": {"type": "string", "description": "手机号码"}, "cycleSignDays": {"type": "integer", "description": "周期内连续签到天数"}}, "required": ["id", "userId", "signDate", "signDays", "<PERSON><PERSON><PERSON><PERSON>", "createTime", "yzCode", "realName", "nickname", "mobile", "cycleSignDays"]}}}, "required": ["recordsTotal", "list"]}}, "required": ["code", "message", "data"]}}}}}, "/operate/sign/earn/list": {"post": {"tags": ["签到配置管理接口"], "summary": "获取签到赚取列表", "description": "", "consumes": ["application/json"], "parameters": [{"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"pageNum": {"type": "integer", "description": "页码", "mock": {"mock": "1"}}, "pageSize": {"type": "integer", "description": "每页数量", "mock": {"mock": "10"}}, "title": {"type": "string", "description": "主标题"}, "ifAllow": {"type": "integer", "description": "是否启用"}, "createUser": {"type": "string", "description": "创建人"}, "startTime": {"type": "string", "description": "开始时间"}, "endTime": {"type": "string", "description": "结束时间"}}, "required": ["pageNum", "pageSize"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总记录数"}, "list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID"}, "title": {"type": "string", "description": "主标题"}, "subTitle": {"type": "string", "description": "副标题"}, "icon": {"type": "string", "description": "图标路由"}, "route": {"type": "string", "description": "路由"}, "sort": {"type": "string", "description": "权重"}, "ifAllow": {"type": "integer", "description": "是否启用"}, "createTime": {"type": "string", "description": "创建时间"}, "createUserId": {"type": "string", "description": "创建人ID"}, "createUser": {"type": "string", "description": "创建人"}}, "required": ["id", "title", "subTitle", "icon", "route", "sort", "ifAllow", "createTime", "createUserId", "createUser"]}}}, "required": ["total", "list"]}}, "required": ["code", "message", "data"]}}}}}, "/operate/sign/config/detail/{id}": {"get": {"tags": ["签到配置管理接口"], "summary": "获取签到配置详情", "description": "获取签到配置列表，支持分页和条件查询", "consumes": ["application/json"], "parameters": [{"name": "id", "in": "path", "description": "", "required": true, "type": "string"}, {"name": "root", "in": "body", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"pageNum": {"type": "integer", "description": "页码", "mock": {"mock": "1"}}, "pageSize": {"type": "integer", "description": "每页数量", "mock": {"mock": "10"}}, "createUser": {"type": "string", "description": "创建人"}, "startTime": {"type": "string", "description": "开始时间"}, "endTime": {"type": "string", "description": "结束时间"}}, "required": ["pageNum", "pageSize"]}}], "responses": {"200": {"description": "successful operation", "schema": {"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"code": {"type": "string"}, "body": {"type": "object", "properties": {"id": {"type": "number", "description": "主键"}, "signRuleDesc": {"type": "string", "description": "签到规则"}, "signTip": {"type": "string", "description": "签到提醒"}, "createTime": {"type": "number"}, "createUserId": {"type": "string"}, "createUser": {"type": "string"}, "updateTime": {"type": "number"}, "updateUserId": {"type": "string"}, "updateUser": {"type": "string"}}, "required": ["signTip", "signRuleDesc"]}, "msg": {"type": "string"}, "ok": {"type": "boolean"}}}}}}}}}